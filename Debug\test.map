******************************************************************************
            TI ARM Clang Linker PC v4.0.0                      
******************************************************************************
>> Linked Wed Jul  2 01:42:20 2025

OUTPUT FILE NAME:   <test.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00003881


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00003ef8  0001c108  R  X
  SRAM                  20200000   00008000  0000118a  00006e76  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00003ef8   00003ef8    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00003b00   00003b00    r-x .text
  00003bc0    00003bc0    000002b0   000002b0    r-- .rodata
  00003e70    00003e70    00000088   00000088    r-- .cinit
20200000    20200000    00000f8a   00000000    rw-
  20200000    20200000    00000800   00000000    rw- .sysmem
  20200800    20200800    00000530   00000000    rw- .bss
  20200d30    20200d30    0000025a   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00003b00     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    00000448     app_motor_usart.o (.text.Deal_data_real)
                  00000ed8    000003b8     libc.a : strtod.c.obj (.text.strtod)
                  00001290    00000220            : _printfi.c.obj (.text._pconv_a)
                  000014b0    000001dc            : _printfi.c.obj (.text._pconv_g)
                  0000168c    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  0000181e    00000002     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00001820    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  0000195c    00000120            : _printfi.c.obj (.text._pconv_e)
                  00001a7c    00000114            : memory.c.obj (.text.aligned_alloc)
                  00001b90    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00001c9c    000000f8     libc.a : fputs.c.obj (.text.fputs)
                  00001d94    000000e8     driverlib.a : dl_timer.o (.text.DL_Timer_initTimerMode)
                  00001e7c    000000e8     libc.a : memory.c.obj (.text.free)
                  00001f64    000000e4     bsp_motor_usart.o (.text.Send_Motor_ArrayU8)
                  00002048    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  0000212c    000000e0     libc.a : setvbuf.c.obj (.text.setvbuf)
                  0000220c    000000d8            : s_scalbn.c.obj (.text.scalbn)
                  000022e4    000000bc     empty.o (.text.main)
                  000023a0    000000a2     libclang_rt.builtins.a : udivmoddi4.S.obj (.text)
                  00002442    0000009a     libc.a : memcpy16.S.obj (.text:memcpy)
                  000024dc    00000004            : pre_init.c.obj (.text._system_pre_init)
                  000024e0    00000088            : strcmp-armv6m.S.obj (.text:strcmp)
                  00002568    0000007c            : fclose.c.obj (.text.__TI_closefile)
                  000025e4    0000007c            : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00002660    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  000026d4    0000000c                            : aeabi_memset.S.obj (.text.__aeabi_memclr)
                  000026e0    00000074                            : truncdfsf2.S.obj (.text.__truncdfsf2)
                  00002754    00000070     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_0_init)
                  000027c4    0000006c     libsysbm.a : hostlseek.c.obj (.text.HOSTlseek)
                  00002830    0000006c                : hostrename.c.obj (.text.HOSTrename)
                  0000289c    0000006c     libc.a : fseek.c.obj (.text.fseeko)
                  00002908    0000006c            : getdevice.c.obj (.text.getdevice)
                  00002974    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  000029dc    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  00002a42    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00002a44    00000064     app_motor_usart.o (.text.Deal_Control_Rxtemp)
                  00002aa8    00000064     libc.a : _io_perm.c.obj (.text.__TI_wrt_ok)
                  00002b0c    00000064            : memory.c.obj (.text.split)
                  00002b70    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  00002bd2    00000062     libc.a : memset16.S.obj (.text:memset)
                  00002c34    00000060     libsysbm.a : hostopen.c.obj (.text.HOSTopen)
                  00002c94    00000060     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_1_init)
                  00002cf4    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  00002d50    0000005c            : printf.c.obj (.text.printf)
                  00002dac    00000058     libsysbm.a : hostread.c.obj (.text.HOSTread)
                  00002e04    00000058                : hostwrite.c.obj (.text.HOSTwrite)
                  00002e5c    00000058     libc.a : _ltoa.c.obj (.text.__TI_ltoa)
                  00002eb4    00000058            : _printfi.c.obj (.text._pconv_f)
                  00002f0c    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  00002f62    00000052     libc.a : fflush.c.obj (.text.__TI_doflush)
                  00002fb4    00000052            : _printfi.c.obj (.text._ecpy)
                  00003006    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  00003008    00000050     libsysbm.a : close.c.obj (.text.close)
                  00003058    0000004c     app_motor_usart.o (.text.Contrl_Speed)
                  000030a4    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  000030ee    00000002     libc.a : _lock.c.obj (.text._nop)
                  000030f0    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00003138    00000048     libsysbm.a : hostclose.c.obj (.text.HOSTclose)
                  00003180    00000048                : hostunlink.c.obj (.text.HOSTunlink)
                  000031c8    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00003210    00000048     app_motor_usart.o (.text.send_upload_data)
                  00003258    00000048     libc.a : strtok.c.obj (.text.strtok)
                  000032a0    00000044     usart.o (.text.UART0_IRQHandler)
                  000032e4    00000044     app_motor_usart.o (.text.send_wheel_diameter)
                  00003328    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00003368    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  000033a8    00000040     libc.a : atoi.c.obj (.text.atoi)
                  000033e8    00000040     delay.o (.text.delay_ms)
                  00003428    00000040     app_motor_usart.o (.text.send_motor_deadzone)
                  00003468    00000040     app_motor_usart.o (.text.send_motor_type)
                  000034a8    00000040     app_motor_usart.o (.text.send_pulse_line)
                  000034e8    00000040     app_motor_usart.o (.text.send_pulse_phase)
                  00003528    0000003c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00003564    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  000035a0    0000003a     libclang_rt.builtins.a : muldsi3.S.obj (.text.__muldsi3)
                  000035da    00000002     --HOLE-- [fill = 0]
                  000035dc    00000038     ti_msp_dl_config.o (.text.SYSCFG_DL_TIMER_0_init)
                  00003614    00000038     libc.a : sprintf.c.obj (.text.sprintf)
                  0000364c    00000034            : fopen.c.obj (.text.__TI_cleanup)
                  00003680    00000034     libsysbm.a : trgmsg.c.obj (.text.__TI_readmsg)
                  000036b4    00000034                : trgmsg.c.obj (.text.__TI_writemsg)
                  000036e8    00000034     libc.a : exit.c.obj (.text.exit)
                  0000371c    00000034            : getdevice.c.obj (.text.finddevice)
                  00003750    00000030     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00003780    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  000037b0    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  000037dc    0000002c     libsysbm.a : unlink.c.obj (.text.unlink)
                  00003808    00000028     libc.a : memory.c.obj (.text.free_list_insert)
                  00003830    00000028     libsysbm.a : lseek.c.obj (.text.lseek)
                  00003858    00000028                : write.c.obj (.text.write)
                  00003880    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  000038a8    00000026            : strspn.c.obj (.text.strspn)
                  000038ce    00000002     --HOLE-- [fill = 0]
                  000038d0    00000024     usart.o (.text.USART_Init)
                  000038f4    00000024     libclang_rt.builtins.a : muldi3.S.obj (.text.__muldi3)
                  00003918    00000024     libc.a : fputs.c.obj (.text.puts)
                  0000393c    00000024            : strcspn.c.obj (.text.strcspn)
                  00003960    00000022            : memccpy.c.obj (.text.memccpy)
                  00003982    00000020     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  000039a2    00000002     --HOLE-- [fill = 0]
                  000039a4    00000020     bsp_motor_usart.o (.text.UART1_IRQHandler)
                  000039c4    00000020     usart.o (.text.fputc)
                  000039e4    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  00003a02    00000002     --HOLE-- [fill = 0]
                  00003a04    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00003a20    0000001c     libc.a : memory.c.obj (.text.free_list_remove)
                  00003a3c    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00003a54    00000018     libc.a : sprintf.c.obj (.text._outs)
                  00003a6c    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  00003a80    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  00003a94    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  00003aa6    00000012     libc.a : copy_decompress_none.c.obj (.text:decompress:none)
                  00003ab8    00000010            : wcslen.c.obj (.text.wcslen)
                  00003ac8    00000010            : copy_zero_init.c.obj (.text:decompress:ZI)
                  00003ad8    0000000e            : memory.c.obj (.text.OUTLINED_FUNCTION_0)
                  00003ae6    00000002     --HOLE-- [fill = 0]
                  00003ae8    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  00003af6    0000000e     libsysbm.a : hostrename.c.obj (.text.strcpy)
                  00003b04    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  00003b12    0000000e     libsysbm.a : hostrename.c.obj (.text.strlen)
                  00003b20    0000000c     libc.a : memory.c.obj (.text.malloc)
                  00003b2c    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00003b36    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  00003b40    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  00003b50    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  00003b5a    0000000a            : sprintf.c.obj (.text._outc)
                  00003b64    00000008            : strtod.c.obj (.text.OUTLINED_FUNCTION_0)
                  00003b6c    00000008            : strtod.c.obj (.text.OUTLINED_FUNCTION_1)
                  00003b74    00000010     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.tramp.__aeabi_dcmpeq.1)
                  00003b84    00000008     libc.a : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  00003b8c    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00003b94    00000008     libc.a : printf.c.obj (.text._outc)
                  00003b9c    00000008            : printf.c.obj (.text._outs)
                  00003ba4    00000008            : fseek.c.obj (.text.fseek)
                  00003bac    00000008     libsysbm.a : remove.c.obj (.text.remove)
                  00003bb4    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00003bb8    00000004     libc.a : exit.c.obj (.text:abort)
                  00003bbc    00000004     --HOLE-- [fill = 0]

.cinit     0    00003e70    00000088     
                  00003e70    00000063     (.cinit..data.load) [load image, compression = lzss]
                  00003ed3    00000001     --HOLE-- [fill = 0]
                  00003ed4    0000000c     (__TI_handler_table)
                  00003ee0    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00003ee8    00000010     (__TI_cinit_table)

.rodata    0    00003bc0    000002b0     
                  00003bc0    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  00003cc1    00000003     ti_msp_dl_config.o (.rodata.gTIMER_0ClockConfig)
                  00003cc4    00000003     app_motor_usart.o (.rodata.str1.38835746890475915111)
                  00003cc7    00000001     --HOLE-- [fill = 0]
                  00003cc8    00000080     libc.a : strtod.c.obj (.rodata.digits)
                  00003d48    00000048            : strtod.c.obj (.rodata.powerof10)
                  00003d90    0000001a     empty.o (.rodata.str1.100506750686581518081)
                  00003daa    00000002     ti_msp_dl_config.o (.rodata.gUART_0ClockConfig)
                  00003dac    00000014     ti_msp_dl_config.o (.rodata.gTIMER_0TimerConfig)
                  00003dc0    00000012     app_motor_usart.o (.rodata.str1.177430081765570553101)
                  00003dd2    00000012     app_motor_usart.o (.rodata.str1.2691616975900509361)
                  00003de4    00000011     libc.a : _printfi.c.obj (.rodata.str1.103488685894817597201)
                  00003df5    00000011            : _printfi.c.obj (.rodata.str1.153638888446227384661)
                  00003e06    00000011     app_motor_usart.o (.rodata.str1.19366273577901094251)
                  00003e17    00000010     empty.o (.rodata.str1.115469792269033204851)
                  00003e27    0000000e     app_motor_usart.o (.rodata.str1.11867396368620600391)
                  00003e35    0000000c     app_motor_usart.o (.rodata.str1.69024998803089298601)
                  00003e41    0000000b     app_motor_usart.o (.rodata.str1.157726972120725782571)
                  00003e4c    0000000b     app_motor_usart.o (.rodata.str1.63670896866782352001)
                  00003e57    00000001     --HOLE-- [fill = 0]
                  00003e58    0000000a     ti_msp_dl_config.o (.rodata.gUART_0Config)
                  00003e62    0000000a     ti_msp_dl_config.o (.rodata.gUART_1Config)
                  00003e6c    00000002     ti_msp_dl_config.o (.rodata.gUART_1ClockConfig)
                  00003e6e    00000002     libc.a : fputs.c.obj (.rodata.str1.166077212684151853901)

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.sysmem    0    20200000    00000800     UNINITIALIZED
                  20200000    00000010     libc.a : memory.c.obj (.sysmem)
                  20200010    000007f0     --HOLE--

.bss       0    20200800    00000530     UNINITIALIZED
                  20200800    00000120     libsysbm.a : trgmsg.c.obj (.bss:_CIOBUF_)
                  20200920    00000100     app_motor_usart.o (.bss.Deal_data_real.data)
                  20200a20    00000100     (.common:g_recv_buff)
                  20200b20    00000100     (.common:g_recv_buff_deal)
                  20200c20    000000a0     libc.a : defs.c.obj (.bss.__TI_tmpnams)
                  20200cc0    00000032     (.common:send_buff)
                  20200cf2    00000001     (.common:g_recv_flag)
                  20200cf3    00000001     --HOLE--
                  20200cf4    00000010     (.common:Encoder_Now)
                  20200d04    00000010     (.common:Encoder_Offset)
                  20200d14    00000010     (.common:g_Speed)
                  20200d24    00000008     libsysbm.a : hostopen.c.obj (.bss.parmbuf)
                  20200d2c    00000004     libc.a : memory.c.obj (.bss.sys_free)

.data      0    20200d30    0000025a     UNINITIALIZED
                  20200d30    000000f0     libc.a : defs.c.obj (.data._ftable)
                  20200e20    00000080     usart.o (.data.recv0_buff)
                  20200ea0    00000078     libsysbm.a : host_device.c.obj (.data._device)
                  20200f18    00000050                : host_device.c.obj (.data._stream)
                  20200f68    00000008     libc.a : exit.c.obj (.data..L_MergedGlobals)
                  20200f70    00000004            : defs.c.obj (.data.__TI_ft_end)
                  20200f74    00000004            : aeabi_portable.c.obj (.data.__aeabi_errno)
                  20200f78    00000004            : _lock.c.obj (.data._lock)
                  20200f7c    00000004            : _lock.c.obj (.data._unlock)
                  20200f80    00000004            : strtok.c.obj (.data.last_end)
                  20200f84    00000002     usart.o (.data.recv0_length)
                  20200f86    00000001     app_motor_usart.o (.data.Deal_Control_Rxtemp.start_flag)
                  20200f87    00000001     app_motor_usart.o (.data.Deal_Control_Rxtemp.step)
                  20200f88    00000001     libc.a : memory.c.obj (.data.memory_is_initialized)
                  20200f89    00000001     usart.o (.data.recv0_flag)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       ti_msp_dl_config.o             500     47        0      
       empty.o                        188     42        0      
       startup_mspm0g350x_ticlang.o   6       192       0      
    +--+------------------------------+-------+---------+---------+
       Total:                         694     281       0      
                                                               
    .\BSP\
       app_motor_usart.o              1668    104       869    
       usart.o                        136     0         131    
       bsp_motor_usart.o              260     0         0      
       delay.o                        64      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2128    104       1000   
                                                               
    C:/ti/mspm0_sdk_2_05_00_05/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                     260     0         0      
       dl_uart.o                      90      0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         360     0         0      
                                                               
    E:\CCSTheia\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                 4510    34        0      
       strtod.c.obj                   968     200       0      
       memory.c.obj                   702     0         5      
       defs.c.obj                     0       0         404    
       fputs.c.obj                    284     2         0      
       aeabi_ctype.S.obj              0       257       0      
       setvbuf.c.obj                  224     0         0      
       s_scalbn.c.obj                 216     0         0      
       getdevice.c.obj                160     0         0      
       memcpy16.S.obj                 154     0         0      
       strcmp-armv6m.S.obj            136     0         0      
       copy_decompress_lzss.c.obj     124     0         0      
       fclose.c.obj                   124     0         0      
       fseek.c.obj                    116     0         0      
       printf.c.obj                   108     0         0      
       _io_perm.c.obj                 100     0         0      
       memset16.S.obj                 98      0         0      
       s_frexp.c.obj                  92      0         0      
       sprintf.c.obj                  90      0         0      
       _ltoa.c.obj                    88      0         0      
       fflush.c.obj                   82      0         0      
       strtok.c.obj                   72      0         4      
       atoi.c.obj                     64      0         0      
       exit.c.obj                     56      0         8      
       autoinit.c.obj                 60      0         0      
       fopen.c.obj                    52      0         0      
       boot_cortex_m.c.obj            40      0         0      
       strspn.c.obj                   38      0         0      
       strcspn.c.obj                  36      0         0      
       memccpy.c.obj                  34      0         0      
       copy_decompress_none.c.obj     18      0         0      
       copy_zero_init.c.obj           16      0         0      
       wcslen.c.obj                   16      0         0      
       aeabi_portable.c.obj           8       0         4      
       _lock.c.obj                    2       0         8      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         8892    493       433    
                                                               
    E:\CCSTheia\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       trgmsg.c.obj                   104     0         288    
       host_device.c.obj              0       0         200    
       hostrename.c.obj               136     0         0      
       hostlseek.c.obj                108     0         0      
       hostopen.c.obj                 96      0         8      
       hostread.c.obj                 88      0         0      
       hostwrite.c.obj                88      0         0      
       close.c.obj                    80      0         0      
       hostclose.c.obj                72      0         0      
       hostunlink.c.obj               72      0         0      
       unlink.c.obj                   44      0         0      
       lseek.c.obj                    40      0         0      
       write.c.obj                    40      0         0      
       remove.c.obj                   8       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         976     0         496    
                                                               
    E:\CCSTheia\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   418     0         0      
       divdf3.S.obj                   268     0         0      
       muldf3.S.obj                   228     0         0      
       comparedf2.c.obj               220     0         0      
       udivmoddi4.S.obj               162     0         0      
       truncdfsf2.S.obj               116     0         0      
       aeabi_dcmp.S.obj               114     0         0      
       aeabi_idivmod.S.obj            86      0         0      
       fixdfsi.S.obj                  74      0         0      
       aeabi_uidivmod.S.obj           64      0         0      
       extendsfdf2.S.obj              64      0         0      
       muldsi3.S.obj                  58      0         0      
       floatsidf.S.obj                44      0         0      
       muldi3.S.obj                   36      0         0      
       ashldi3.S.obj                  30      0         0      
       aeabi_memset.S.obj             26      0         0      
       aeabi_uldivmod.S.obj           20      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2040    0         0      
                                                               
       Heap:                          0       0         2048   
       Stack:                         0       0         512    
       Linker Generated:              0       135       0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   15090   1013      4489   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00003ee8 records: 2, size/record: 8, table size: 16
	.data: load addr=00003e70, load size=00000063 bytes, run addr=20200d30, run size=0000025a bytes, compression=lzss
	.bss: load addr=00003ee0, load size=00000008 bytes, run addr=20200800, run size=00000530 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00003ed4 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   0000168d     00003b40     00003b3e   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
__aeabi_dcmpeq            $Tramp$TT$L$PI$$__aeabi_dcmpeq
   00002b71     00003b74     00003b72   libc.a : strtod.c.obj (.text.OUTLINED_FUNCTION_1)

[2 trampolines]
[2 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                          
-------   ----                          
0000181f  ADC0_IRQHandler               
0000181f  ADC1_IRQHandler               
0000181f  AES_IRQHandler                
00003bb8  C$$EXIT                       
000036e1  C$$IO$$                       
0000181f  CANFD0_IRQHandler             
00003059  Contrl_Speed                  
0000181f  DAC0_IRQHandler               
00003b2d  DL_Common_delayCycles         
00001d95  DL_Timer_initTimerMode        
00003a05  DL_Timer_setClockConfig       
000030f1  DL_UART_init                  
00003a95  DL_UART_setClockConfig        
0000181f  DMA_IRQHandler                
00002a45  Deal_Control_Rxtemp           
00000a91  Deal_data_real                
0000181f  Default_Handler               
20200cf4  Encoder_Now                   
20200d04  Encoder_Offset                
0000181f  GROUP0_IRQHandler             
0000181f  GROUP1_IRQHandler             
00003139  HOSTclose                     
000027c5  HOSTlseek                     
00002c35  HOSTopen                      
00002dad  HOSTread                      
00002831  HOSTrename                    
00003181  HOSTunlink                    
00002e05  HOSTwrite                     
0000181f  HardFault_Handler             
0000181f  I2C0_IRQHandler               
0000181f  I2C1_IRQHandler               
0000181f  NMI_Handler                   
0000181f  PendSV_Handler                
0000181f  RTC_IRQHandler                
00003bb5  Reset_Handler                 
0000181f  SPI0_IRQHandler               
0000181f  SPI1_IRQHandler               
0000181f  SVC_Handler                   
00003a3d  SYSCFG_DL_GPIO_init           
00003529  SYSCFG_DL_SYSCTL_init         
00003751  SYSCFG_DL_SYSTICK_init        
000035dd  SYSCFG_DL_TIMER_0_init        
00002755  SYSCFG_DL_UART_0_init         
00002c95  SYSCFG_DL_UART_1_init         
00003983  SYSCFG_DL_init                
000031c9  SYSCFG_DL_initPower           
00001f65  Send_Motor_ArrayU8            
0000181f  SysTick_Handler               
0000181f  TIMA0_IRQHandler              
0000181f  TIMA1_IRQHandler              
0000181f  TIMG0_IRQHandler              
0000181f  TIMG12_IRQHandler             
0000181f  TIMG6_IRQHandler              
0000181f  TIMG7_IRQHandler              
0000181f  TIMG8_IRQHandler              
000032a1  UART0_IRQHandler              
000039a5  UART1_IRQHandler              
0000181f  UART2_IRQHandler              
0000181f  UART3_IRQHandler              
000038d1  USART_Init                    
20200800  _CIOBUF_                      
20200800  __CIOBUF_                     
20208000  __STACK_END                   
00000200  __STACK_SIZE                  
00000800  __SYSMEM_SIZE                 
00000000  __TI_ATRegion0_region_sz      
00000000  __TI_ATRegion0_src_addr       
00000000  __TI_ATRegion0_trg_addr       
00000000  __TI_ATRegion1_region_sz      
00000000  __TI_ATRegion1_src_addr       
00000000  __TI_ATRegion1_trg_addr       
00000000  __TI_ATRegion2_region_sz      
00000000  __TI_ATRegion2_src_addr       
00000000  __TI_ATRegion2_trg_addr       
00003ee8  __TI_CINIT_Base               
00003ef8  __TI_CINIT_Limit              
00003ef8  __TI_CINIT_Warm               
00003ed4  __TI_Handler_Table_Base       
00003ee0  __TI_Handler_Table_Limit      
00003565  __TI_auto_init_nobinit_nopinit
0000364d  __TI_cleanup                  
20200f68  __TI_cleanup_ptr              
00002569  __TI_closefile                
000025e5  __TI_decompress_lzss          
00003aa7  __TI_decompress_none          
00002f63  __TI_doflush                  
20200f6c  __TI_dtors_ptr                
20200f70  __TI_ft_end                   
00002e5d  __TI_ltoa                     
ffffffff  __TI_pprof_out_hndl           
000000c1  __TI_printfi                  
ffffffff  __TI_prof_data_size           
ffffffff  __TI_prof_data_start          
00003681  __TI_readmsg                  
00000000  __TI_static_base__            
20200c20  __TI_tmpnams                  
000036b5  __TI_writemsg                 
00002aa9  __TI_wrt_ok                   
00003ac9  __TI_zero_init                
00001697  __adddf3                      
00003bc0  __aeabi_ctype_table_          
00003bc0  __aeabi_ctype_table_C         
000026e1  __aeabi_d2f                   
000030a5  __aeabi_d2iz                  
00001697  __aeabi_dadd                  
00002b71  __aeabi_dcmpeq                
00002bad  __aeabi_dcmpge                
00002bc1  __aeabi_dcmpgt                
00002b99  __aeabi_dcmple                
00002b85  __aeabi_dcmplt                
00001b91  __aeabi_ddiv                  
00002049  __aeabi_dmul                  
0000168d  __aeabi_dsub                  
20200f74  __aeabi_errno                 
00003b85  __aeabi_errno_addr            
00003369  __aeabi_f2d                   
000037b1  __aeabi_i2d                   
00002f0d  __aeabi_idiv                  
00002a43  __aeabi_idiv0                 
00002f0d  __aeabi_idivmod               
00003007  __aeabi_ldiv0                 
000039e5  __aeabi_llsl                  
000038f5  __aeabi_lmul                  
000026d5  __aeabi_memclr                
000026d5  __aeabi_memclr4               
000026d5  __aeabi_memclr8               
00003b8d  __aeabi_memcpy                
00003b8d  __aeabi_memcpy4               
00003b8d  __aeabi_memcpy8               
00003ae9  __aeabi_memset                
00003ae9  __aeabi_memset4               
00003ae9  __aeabi_memset8               
00003329  __aeabi_uidiv                 
00003329  __aeabi_uidivmod              
00003a6d  __aeabi_uldivmod              
000039e5  __ashldi3                     
ffffffff  __binit__                     
00002975  __cmpdf2                      
00001b91  __divdf3                      
00002975  __eqdf2                       
00003369  __extendsfdf2                 
000030a5  __fixdfsi                     
000037b1  __floatsidf                   
00002661  __gedf2                       
00002661  __gtdf2                       
00002975  __ledf2                       
00002975  __ltdf2                       
UNDEFED   __mpu_init                    
00002049  __muldf3                      
000038f5  __muldi3                      
000035a1  __muldsi3                     
00002975  __nedf2                       
20207e00  __stack                       
20200000  __start___llvm_prf_bits       
20200000  __start___llvm_prf_cnts       
20200000  __stop___llvm_prf_bits        
20200000  __stop___llvm_prf_cnts        
0000168d  __subdf3                      
000026e1  __truncdfsf2                  
000023a1  __udivmoddi4                  
00003881  _c_int00_noargs               
20200ea0  _device                       
20200d30  _ftable                       
20200f78  _lock                         
000030ef  _nop                          
20200f18  _stream                       
20200000  _sys_memory                   
UNDEFED   _system_post_cinit            
000024dd  _system_pre_init              
20200f7c  _unlock                       
00003bb9  abort                         
00001a7d  aligned_alloc                 
000033a9  atoi                          
ffffffff  binit                         
00003009  close                         
000033e9  delay_ms                      
000036e9  exit                          
0000371d  finddevice                    
000039c5  fputc                         
00001c9d  fputs                         
00001e7d  free                          
00002cf5  frexp                         
00002cf5  frexpl                        
00003ba5  fseek                         
0000289d  fseeko                        
20200d14  g_Speed                       
20200a20  g_recv_buff                   
20200b20  g_recv_buff_deal              
20200cf2  g_recv_flag                   
00002909  getdevice                     
00000000  interruptVectors              
0000220d  ldexp                         
0000220d  ldexpl                        
00003831  lseek                         
000022e5  main                          
00003b21  malloc                        
00001a7d  memalign                      
00003961  memccpy                       
00002443  memcpy                        
00002bd3  memset                        
20200d24  parmbuf                       
00002d51  printf                        
00003919  puts                          
20200e20  recv0_buff                    
20200f89  recv0_flag                    
20200f84  recv0_length                  
00003bad  remove                        
0000220d  scalbn                        
0000220d  scalbnl                       
20200cc0  send_buff                     
00003429  send_motor_deadzone           
00003469  send_motor_type               
000034a9  send_pulse_line               
000034e9  send_pulse_phase              
00003211  send_upload_data              
000032e5  send_wheel_diameter           
0000212d  setvbuf                       
00003615  sprintf                       
000024e1  strcmp                        
0000393d  strcspn                       
000038a9  strspn                        
00000ed9  strtod                        
00003259  strtok                        
00000ed9  strtold                       
000037dd  unlink                        
00003ab9  wcslen                        
00003859  write                         


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                          
-------   ----                          
00000000  __TI_ATRegion0_region_sz      
00000000  __TI_ATRegion0_src_addr       
00000000  __TI_ATRegion0_trg_addr       
00000000  __TI_ATRegion1_region_sz      
00000000  __TI_ATRegion1_src_addr       
00000000  __TI_ATRegion1_trg_addr       
00000000  __TI_ATRegion2_region_sz      
00000000  __TI_ATRegion2_src_addr       
00000000  __TI_ATRegion2_trg_addr       
00000000  __TI_static_base__            
00000000  interruptVectors              
000000c1  __TI_printfi                  
00000200  __STACK_SIZE                  
00000800  __SYSMEM_SIZE                 
00000a91  Deal_data_real                
00000ed9  strtod                        
00000ed9  strtold                       
0000168d  __aeabi_dsub                  
0000168d  __subdf3                      
00001697  __adddf3                      
00001697  __aeabi_dadd                  
0000181f  ADC0_IRQHandler               
0000181f  ADC1_IRQHandler               
0000181f  AES_IRQHandler                
0000181f  CANFD0_IRQHandler             
0000181f  DAC0_IRQHandler               
0000181f  DMA_IRQHandler                
0000181f  Default_Handler               
0000181f  GROUP0_IRQHandler             
0000181f  GROUP1_IRQHandler             
0000181f  HardFault_Handler             
0000181f  I2C0_IRQHandler               
0000181f  I2C1_IRQHandler               
0000181f  NMI_Handler                   
0000181f  PendSV_Handler                
0000181f  RTC_IRQHandler                
0000181f  SPI0_IRQHandler               
0000181f  SPI1_IRQHandler               
0000181f  SVC_Handler                   
0000181f  SysTick_Handler               
0000181f  TIMA0_IRQHandler              
0000181f  TIMA1_IRQHandler              
0000181f  TIMG0_IRQHandler              
0000181f  TIMG12_IRQHandler             
0000181f  TIMG6_IRQHandler              
0000181f  TIMG7_IRQHandler              
0000181f  TIMG8_IRQHandler              
0000181f  UART2_IRQHandler              
0000181f  UART3_IRQHandler              
00001a7d  aligned_alloc                 
00001a7d  memalign                      
00001b91  __aeabi_ddiv                  
00001b91  __divdf3                      
00001c9d  fputs                         
00001d95  DL_Timer_initTimerMode        
00001e7d  free                          
00001f65  Send_Motor_ArrayU8            
00002049  __aeabi_dmul                  
00002049  __muldf3                      
0000212d  setvbuf                       
0000220d  ldexp                         
0000220d  ldexpl                        
0000220d  scalbn                        
0000220d  scalbnl                       
000022e5  main                          
000023a1  __udivmoddi4                  
00002443  memcpy                        
000024dd  _system_pre_init              
000024e1  strcmp                        
00002569  __TI_closefile                
000025e5  __TI_decompress_lzss          
00002661  __gedf2                       
00002661  __gtdf2                       
000026d5  __aeabi_memclr                
000026d5  __aeabi_memclr4               
000026d5  __aeabi_memclr8               
000026e1  __aeabi_d2f                   
000026e1  __truncdfsf2                  
00002755  SYSCFG_DL_UART_0_init         
000027c5  HOSTlseek                     
00002831  HOSTrename                    
0000289d  fseeko                        
00002909  getdevice                     
00002975  __cmpdf2                      
00002975  __eqdf2                       
00002975  __ledf2                       
00002975  __ltdf2                       
00002975  __nedf2                       
00002a43  __aeabi_idiv0                 
00002a45  Deal_Control_Rxtemp           
00002aa9  __TI_wrt_ok                   
00002b71  __aeabi_dcmpeq                
00002b85  __aeabi_dcmplt                
00002b99  __aeabi_dcmple                
00002bad  __aeabi_dcmpge                
00002bc1  __aeabi_dcmpgt                
00002bd3  memset                        
00002c35  HOSTopen                      
00002c95  SYSCFG_DL_UART_1_init         
00002cf5  frexp                         
00002cf5  frexpl                        
00002d51  printf                        
00002dad  HOSTread                      
00002e05  HOSTwrite                     
00002e5d  __TI_ltoa                     
00002f0d  __aeabi_idiv                  
00002f0d  __aeabi_idivmod               
00002f63  __TI_doflush                  
00003007  __aeabi_ldiv0                 
00003009  close                         
00003059  Contrl_Speed                  
000030a5  __aeabi_d2iz                  
000030a5  __fixdfsi                     
000030ef  _nop                          
000030f1  DL_UART_init                  
00003139  HOSTclose                     
00003181  HOSTunlink                    
000031c9  SYSCFG_DL_initPower           
00003211  send_upload_data              
00003259  strtok                        
000032a1  UART0_IRQHandler              
000032e5  send_wheel_diameter           
00003329  __aeabi_uidiv                 
00003329  __aeabi_uidivmod              
00003369  __aeabi_f2d                   
00003369  __extendsfdf2                 
000033a9  atoi                          
000033e9  delay_ms                      
00003429  send_motor_deadzone           
00003469  send_motor_type               
000034a9  send_pulse_line               
000034e9  send_pulse_phase              
00003529  SYSCFG_DL_SYSCTL_init         
00003565  __TI_auto_init_nobinit_nopinit
000035a1  __muldsi3                     
000035dd  SYSCFG_DL_TIMER_0_init        
00003615  sprintf                       
0000364d  __TI_cleanup                  
00003681  __TI_readmsg                  
000036b5  __TI_writemsg                 
000036e1  C$$IO$$                       
000036e9  exit                          
0000371d  finddevice                    
00003751  SYSCFG_DL_SYSTICK_init        
000037b1  __aeabi_i2d                   
000037b1  __floatsidf                   
000037dd  unlink                        
00003831  lseek                         
00003859  write                         
00003881  _c_int00_noargs               
000038a9  strspn                        
000038d1  USART_Init                    
000038f5  __aeabi_lmul                  
000038f5  __muldi3                      
00003919  puts                          
0000393d  strcspn                       
00003961  memccpy                       
00003983  SYSCFG_DL_init                
000039a5  UART1_IRQHandler              
000039c5  fputc                         
000039e5  __aeabi_llsl                  
000039e5  __ashldi3                     
00003a05  DL_Timer_setClockConfig       
00003a3d  SYSCFG_DL_GPIO_init           
00003a6d  __aeabi_uldivmod              
00003a95  DL_UART_setClockConfig        
00003aa7  __TI_decompress_none          
00003ab9  wcslen                        
00003ac9  __TI_zero_init                
00003ae9  __aeabi_memset                
00003ae9  __aeabi_memset4               
00003ae9  __aeabi_memset8               
00003b21  malloc                        
00003b2d  DL_Common_delayCycles         
00003b85  __aeabi_errno_addr            
00003b8d  __aeabi_memcpy                
00003b8d  __aeabi_memcpy4               
00003b8d  __aeabi_memcpy8               
00003ba5  fseek                         
00003bad  remove                        
00003bb5  Reset_Handler                 
00003bb8  C$$EXIT                       
00003bb9  abort                         
00003bc0  __aeabi_ctype_table_          
00003bc0  __aeabi_ctype_table_C         
00003ed4  __TI_Handler_Table_Base       
00003ee0  __TI_Handler_Table_Limit      
00003ee8  __TI_CINIT_Base               
00003ef8  __TI_CINIT_Limit              
00003ef8  __TI_CINIT_Warm               
20200000  __start___llvm_prf_bits       
20200000  __start___llvm_prf_cnts       
20200000  __stop___llvm_prf_bits        
20200000  __stop___llvm_prf_cnts        
20200000  _sys_memory                   
20200800  _CIOBUF_                      
20200800  __CIOBUF_                     
20200a20  g_recv_buff                   
20200b20  g_recv_buff_deal              
20200c20  __TI_tmpnams                  
20200cc0  send_buff                     
20200cf2  g_recv_flag                   
20200cf4  Encoder_Now                   
20200d04  Encoder_Offset                
20200d14  g_Speed                       
20200d24  parmbuf                       
20200d30  _ftable                       
20200e20  recv0_buff                    
20200ea0  _device                       
20200f18  _stream                       
20200f68  __TI_cleanup_ptr              
20200f6c  __TI_dtors_ptr                
20200f70  __TI_ft_end                   
20200f74  __aeabi_errno                 
20200f78  _lock                         
20200f7c  _unlock                       
20200f84  recv0_length                  
20200f89  recv0_flag                    
20207e00  __stack                       
20208000  __STACK_END                   
ffffffff  __TI_pprof_out_hndl           
ffffffff  __TI_prof_data_size           
ffffffff  __TI_prof_data_start          
ffffffff  __binit__                     
ffffffff  binit                         
UNDEFED   __mpu_init                    
UNDEFED   _system_post_cinit            

[227 symbols]
